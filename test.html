<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ant Design 本地化测试页面</title>
    
    <!-- 本地化的Ant Design CSS -->
    <link rel="stylesheet" href="lib/antd/reset.css">
    
    <!-- React 核心库 -->
    <script src="lib/react/react.production.min.js"></script>
    <script src="lib/react/react-dom.production.min.js"></script>
    <script src="lib/react/lodash.min.js"></script>
    
    <!-- Ant Design 核心库 -->
    <script src="lib/antd/antd.min.js"></script>
    
    <!-- Ant Design Charts -->
    <script src="lib/antd-charts/charts.min.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 8px;
        }
        
        .demo-item {
            margin-bottom: 16px;
        }
        
        .chart-container {
            height: 300px;
            margin: 16px 0;
        }
        
        .form-container {
            max-width: 600px;
        }
        
        .table-container {
            margin: 16px 0;
        }
        
        .button-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin: 16px 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success {
            background-color: #52c41a;
        }
        
        .status-error {
            background-color: #ff4d4f;
        }
        
        .status-warning {
            background-color: #faad14;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Ant Design 本地化测试页面</h1>
        <p>此页面用于测试Ant Design和Ant Design Charts的本地化配置是否正常工作。</p>
        
        <!-- 状态指示器 -->
        <div class="section">
            <div class="section-title">加载状态检查</div>
            <div id="status-check">
                <div class="demo-item">
                    <span class="status-indicator status-warning"></span>
                    <span id="react-status">React: 检查中...</span>
                </div>
                <div class="demo-item">
                    <span class="status-indicator status-warning"></span>
                    <span id="antd-status">Ant Design: 检查中...</span>
                </div>
                <div class="demo-item">
                    <span class="status-indicator status-warning"></span>
                    <span id="charts-status">Ant Design Charts: 检查中...</span>
                </div>
            </div>
        </div>
        
        <!-- 基础UI组件测试 -->
        <div class="section">
            <div class="section-title">基础UI组件测试</div>
            <div id="basic-components"></div>
        </div>
        
        <!-- 表单组件测试 -->
        <div class="section">
            <div class="section-title">表单组件测试</div>
            <div id="form-components" class="form-container"></div>
        </div>
        
        <!-- 表格组件测试 -->
        <div class="section">
            <div class="section-title">表格组件测试</div>
            <div id="table-components" class="table-container"></div>
        </div>
        
        <!-- 图表组件测试 -->
        <div class="section">
            <div class="section-title">图表组件测试</div>
            <div id="chart-components"></div>
        </div>
        
        <!-- 模态框测试 -->
        <div class="section">
            <div class="section-title">模态框和交互组件测试</div>
            <div id="modal-components"></div>
        </div>
    </div>

    <script>
        // 检查库加载状态
        function checkLibraryStatus() {
            const reactStatus = document.getElementById('react-status');
            const antdStatus = document.getElementById('antd-status');
            const chartsStatus = document.getElementById('charts-status');
            
            // 检查React
            if (typeof React !== 'undefined' && typeof ReactDOM !== 'undefined') {
                reactStatus.textContent = 'React: ✅ 加载成功';
                reactStatus.previousElementSibling.className = 'status-indicator status-success';
            } else {
                reactStatus.textContent = 'React: ❌ 加载失败';
                reactStatus.previousElementSibling.className = 'status-indicator status-error';
            }
            
            // 检查Ant Design
            if (typeof antd !== 'undefined') {
                antdStatus.textContent = 'Ant Design: ✅ 加载成功';
                antdStatus.previousElementSibling.className = 'status-indicator status-success';
            } else {
                antdStatus.textContent = 'Ant Design: ❌ 加载失败';
                antdStatus.previousElementSibling.className = 'status-indicator status-error';
            }
            
            // 检查Ant Design Charts
            if (typeof Charts !== 'undefined') {
                chartsStatus.textContent = 'Ant Design Charts: ✅ 加载成功';
                chartsStatus.previousElementSibling.className = 'status-indicator status-success';
            } else {
                chartsStatus.textContent = 'Ant Design Charts: ❌ 加载失败';
                chartsStatus.previousElementSibling.className = 'status-indicator status-error';
            }
        }
        
        // 页面加载完成后检查状态
        window.addEventListener('load', function() {
            setTimeout(checkLibraryStatus, 100);
            
            // 如果库加载成功，初始化组件
            if (typeof React !== 'undefined' && typeof antd !== 'undefined') {
                initializeComponents();
            }
        });
        
        // 初始化组件
        function initializeComponents() {
            try {
                initBasicComponents();
                initFormComponents();
                initTableComponents();
                initModalComponents();
                
                // 延迟初始化图表组件，确保DOM已准备好
                setTimeout(initChartComponents, 500);
            } catch (error) {
                console.error('组件初始化失败:', error);
            }
        }
        
        // 基础组件初始化
        function initBasicComponents() {
            const { Button, Space, Tag, Alert, Badge, Tooltip } = antd;
            const { createElement: h } = React;

            const basicComponentsElement = React.createElement('div', null,
                h('div', { className: 'demo-item' },
                    h('h4', null, '按钮组件'),
                    h('div', { className: 'button-group' },
                        h(Button, { type: 'primary' }, '主要按钮'),
                        h(Button, { type: 'default' }, '默认按钮'),
                        h(Button, { type: 'dashed' }, '虚线按钮'),
                        h(Button, { type: 'text' }, '文本按钮'),
                        h(Button, { type: 'link' }, '链接按钮'),
                        h(Button, { danger: true }, '危险按钮'),
                        h(Button, { loading: true }, '加载中'),
                        h(Button, { disabled: true }, '禁用按钮')
                    )
                ),
                h('div', { className: 'demo-item' },
                    h('h4', null, '标签和徽章'),
                    h(Space, { wrap: true },
                        h(Tag, { color: 'blue' }, '蓝色标签'),
                        h(Tag, { color: 'green' }, '绿色标签'),
                        h(Tag, { color: 'red' }, '红色标签'),
                        h(Badge, { count: 5 }, h(Button, null, '消息')),
                        h(Badge, { dot: true }, h(Button, null, '通知')),
                        h(Tooltip, { title: '这是一个提示' }, h(Button, null, '悬停提示'))
                    )
                ),
                h('div', { className: 'demo-item' },
                    h('h4', null, '警告提示'),
                    h(Alert, {
                        message: '成功提示',
                        type: 'success',
                        showIcon: true,
                        style: { marginBottom: '8px' }
                    }),
                    h(Alert, {
                        message: '信息提示',
                        type: 'info',
                        showIcon: true,
                        style: { marginBottom: '8px' }
                    }),
                    h(Alert, {
                        message: '警告提示',
                        type: 'warning',
                        showIcon: true,
                        style: { marginBottom: '8px' }
                    }),
                    h(Alert, {
                        message: '错误提示',
                        type: 'error',
                        showIcon: true
                    })
                )
            );

            ReactDOM.render(basicComponentsElement, document.getElementById('basic-components'));
        }

        // 表单组件初始化
        function initFormComponents() {
            const { Form, Input, Select, DatePicker, Switch, Checkbox, Radio, Button, InputNumber } = antd;
            const { Option } = Select;
            const { createElement: h } = React;

            const formElement = h(Form, {
                layout: 'vertical',
                initialValues: {
                    username: 'demo',
                    email: '<EMAIL>',
                    age: 25,
                    gender: 'male',
                    skills: ['javascript', 'react'],
                    newsletter: true
                }
            },
                h(Form.Item, {
                    label: '用户名',
                    name: 'username',
                    rules: [{ required: true, message: '请输入用户名!' }]
                }, h(Input, { placeholder: '请输入用户名' })),

                h(Form.Item, {
                    label: '邮箱',
                    name: 'email',
                    rules: [{ required: true, type: 'email', message: '请输入有效的邮箱地址!' }]
                }, h(Input, { placeholder: '请输入邮箱' })),

                h(Form.Item, { label: '年龄', name: 'age' },
                    h(InputNumber, { min: 1, max: 120, style: { width: '100%' } })
                ),

                h(Form.Item, { label: '性别', name: 'gender' },
                    h(Radio.Group, null,
                        h(Radio, { value: 'male' }, '男'),
                        h(Radio, { value: 'female' }, '女')
                    )
                ),

                h(Form.Item, { label: '技能', name: 'skills' },
                    h(Select, { mode: 'multiple', placeholder: '请选择技能' },
                        h(Option, { value: 'javascript' }, 'JavaScript'),
                        h(Option, { value: 'react' }, 'React'),
                        h(Option, { value: 'vue' }, 'Vue'),
                        h(Option, { value: 'angular' }, 'Angular'),
                        h(Option, { value: 'nodejs' }, 'Node.js')
                    )
                ),

                h(Form.Item, { label: '生日', name: 'birthday' },
                    h(DatePicker, { style: { width: '100%' } })
                ),

                h(Form.Item, { name: 'newsletter', valuePropName: 'checked' },
                    h(Checkbox, null, '订阅邮件通知')
                ),

                h(Form.Item, null,
                    h(Space, null,
                        h(Button, { type: 'primary', htmlType: 'submit' }, '提交'),
                        h(Button, { htmlType: 'button' }, '重置')
                    )
                )
            );

            ReactDOM.render(formElement, document.getElementById('form-components'));
        }

        // 表格组件初始化
        function initTableComponents() {
            const { Table, Tag, Space, Button } = antd;
            const { createElement: h } = React;

            const columns = [
                {
                    title: '姓名',
                    dataIndex: 'name',
                    key: 'name',
                    render: (text) => h('a', null, text)
                },
                {
                    title: '年龄',
                    dataIndex: 'age',
                    key: 'age'
                },
                {
                    title: '地址',
                    dataIndex: 'address',
                    key: 'address'
                },
                {
                    title: '标签',
                    key: 'tags',
                    dataIndex: 'tags',
                    render: (tags) => h(Space, null, ...tags.map(tag => {
                        const color = tag.length > 5 ? 'geekblue' : 'green';
                        return h(Tag, { color: color, key: tag }, tag.toUpperCase());
                    }))
                },
                {
                    title: '操作',
                    key: 'action',
                    render: (text, record) => h(Space, { size: 'middle' },
                        h('a', null, '编辑'),
                        h('a', null, '删除')
                    )
                }
            ];

            const data = [
                {
                    key: '1',
                    name: '张三',
                    age: 32,
                    address: '北京市朝阳区',
                    tags: ['开发者', 'React']
                },
                {
                    key: '2',
                    name: '李四',
                    age: 42,
                    address: '上海市浦东新区',
                    tags: ['设计师']
                },
                {
                    key: '3',
                    name: '王五',
                    age: 32,
                    address: '广州市天河区',
                    tags: ['产品经理', 'UI/UX', '敏捷开发']
                }
            ];

            const tableElement = h(Table, {
                columns: columns,
                dataSource: data,
                pagination: { pageSize: 5 }
            });

            ReactDOM.render(tableElement, document.getElementById('table-components'));
        }

        // 模态框组件初始化
        function initModalComponents() {
            const { Button, Modal, message, notification, Popconfirm, Drawer } = antd;
            const { createElement: h } = React;

            function showModal() {
                Modal.info({
                    title: '这是一个信息模态框',
                    content: h('div', null,
                        h('p', null, '这是模态框的内容。'),
                        h('p', null, '这是更多的内容。')
                    ),
                    onOk() {
                        console.log('确定');
                    }
                });
            }

            function showConfirm() {
                Modal.confirm({
                    title: '确认删除',
                    content: '确定要删除这个项目吗？',
                    okText: '确定',
                    cancelText: '取消',
                    onOk() {
                        message.success('删除成功');
                    },
                    onCancel() {
                        message.info('取消删除');
                    }
                });
            }

            function showMessage() {
                message.success('这是一个成功消息');
            }

            function showNotification() {
                notification.open({
                    message: '通知标题',
                    description: '这是通知的详细描述信息，可以包含更多的内容。',
                    duration: 4.5
                });
            }

            function confirm() {
                message.success('点击了确认');
            }

            function cancel() {
                message.error('点击了取消');
            }

            const modalElement = h('div', null,
                h('div', { className: 'demo-item' },
                    h('h4', null, '模态框'),
                    h(Space, null,
                        h(Button, { type: 'primary', onClick: showModal }, '信息模态框'),
                        h(Button, { onClick: showConfirm }, '确认模态框')
                    )
                ),
                h('div', { className: 'demo-item' },
                    h('h4', null, '消息和通知'),
                    h(Space, null,
                        h(Button, { onClick: showMessage }, '显示消息'),
                        h(Button, { onClick: showNotification }, '显示通知')
                    )
                ),
                h('div', { className: 'demo-item' },
                    h('h4', null, '气泡确认框'),
                    h(Popconfirm, {
                        title: '确定要删除这个任务吗？',
                        onConfirm: confirm,
                        onCancel: cancel,
                        okText: '确定',
                        cancelText: '取消'
                    }, h(Button, { danger: true }, '删除'))
                )
            );

            ReactDOM.render(modalElement, document.getElementById('modal-components'));
        }

        // 图表组件初始化
        function initChartComponents() {
            if (typeof Charts === 'undefined') {
                document.getElementById('chart-components').innerHTML =
                    '<div style="color: red;">❌ Ant Design Charts 未正确加载</div>';
                return;
            }

            const { createElement: h } = React;

            // 柱状图数据
            const columnData = [
                { year: '1991', value: 3 },
                { year: '1992', value: 4 },
                { year: '1993', value: 3.5 },
                { year: '1994', value: 5 },
                { year: '1995', value: 4.9 },
                { year: '1996', value: 6 },
                { year: '1997', value: 7 },
                { year: '1998', value: 9 },
                { year: '1999', value: 13 }
            ];

            // 折线图数据
            const lineData = [
                { year: '1991', value: 3 },
                { year: '1992', value: 4 },
                { year: '1993', value: 3.5 },
                { year: '1994', value: 5 },
                { year: '1995', value: 4.9 },
                { year: '1996', value: 6 },
                { year: '1997', value: 7 },
                { year: '1998', value: 9 },
                { year: '1999', value: 13 }
            ];

            // 饼图数据
            const pieData = [
                { type: '分类一', value: 27 },
                { type: '分类二', value: 25 },
                { type: '分类三', value: 18 },
                { type: '分类四', value: 15 },
                { type: '分类五', value: 10 },
                { type: '其他', value: 5 }
            ];

            try {
                const chartElement = h('div', null,
                    h('div', { className: 'demo-item' },
                        h('h4', null, '柱状图'),
                        h('div', { className: 'chart-container', id: 'column-chart' })
                    ),
                    h('div', { className: 'demo-item' },
                        h('h4', null, '折线图'),
                        h('div', { className: 'chart-container', id: 'line-chart' })
                    ),
                    h('div', { className: 'demo-item' },
                        h('h4', null, '饼图'),
                        h('div', { className: 'chart-container', id: 'pie-chart' })
                    )
                );

                ReactDOM.render(chartElement, document.getElementById('chart-components'));

                // 延迟渲染图表，确保DOM元素已创建
                setTimeout(() => {
                    // 柱状图
                    if (Charts.Column) {
                        const columnChart = h(Charts.Column, {
                            data: columnData,
                            xField: 'year',
                            yField: 'value',
                            height: 250
                        });
                        ReactDOM.render(columnChart, document.getElementById('column-chart'));
                    }

                    // 折线图
                    if (Charts.Line) {
                        const lineChart = h(Charts.Line, {
                            data: lineData,
                            xField: 'year',
                            yField: 'value',
                            height: 250,
                            point: { size: 5, shape: 'diamond' }
                        });
                        ReactDOM.render(lineChart, document.getElementById('line-chart'));
                    }

                    // 饼图
                    if (Charts.Pie) {
                        const pieChart = h(Charts.Pie, {
                            data: pieData,
                            angleField: 'value',
                            colorField: 'type',
                            height: 250,
                            radius: 0.8,
                            label: {
                                type: 'outer',
                                content: '{name} {percentage}'
                            }
                        });
                        ReactDOM.render(pieChart, document.getElementById('pie-chart'));
                    }
                }, 100);

            } catch (error) {
                console.error('图表初始化失败:', error);
                document.getElementById('chart-components').innerHTML =
                    '<div style="color: red;">❌ 图表组件初始化失败: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>
