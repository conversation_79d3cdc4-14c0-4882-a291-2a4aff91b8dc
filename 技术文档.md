# 设备综合管理系统技术文档

## 1. 项目概述

### 项目名称
**设备综合管理系统 (Equipment Management System)**

### 项目目的
一个基于Web的企业级设备综合管理平台，用于管理工厂设备的交接、故障处理、备品管理、参数监控等全生命周期业务流程。系统采用模块化设计，支持多数据源连接，提供完整的设备管理解决方案。

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Frontend)                      │
├─────────────────────────────────────────────────────────────┤
│  HTML5 + CSS3 + JavaScript + Layui + jQuery + ECharts      │
│  ├─ 用户界面组件                                              │
│  ├─ 数据可视化图表                                            │
│  ├─ 表单验证与交互                                            │
│  └─ 权限控制与路由                                            │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        API接口层 (Backend)                   │
├─────────────────────────────────────────────────────────────┤
│                        PHP 7.4+                            │
│  ├─ RESTful API接口 (80+ 接口)                               │
│  ├─ 数据验证与处理                                           │
│  ├─ 文件上传下载                                             │
│  ├─ 会话管理与权限控制                                        │
│  └─ 错误处理与日志记录                                        │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                       数据存储层 (Database)                   │
├─────────────────────────────────────────────────────────────┤
│  MySQL 8.0+ (主数据库)          SQL Server (生产数据)        │
│  ├─ 用户管理 (loginlist)        ├─ Tact Time数据             │
│  ├─ 交接记录 (associates)       ├─ BOM数据                   │
│  ├─ 故障管理 (faults)           └─ 生产参数                  │
│  ├─ 备品管理 (spareparts)                                    │
│  ├─ 通知管理 (notices)                                       │
│  ├─ 参数监控 (eqp_data)                                      │
│  └─ 系统配置                                                 │
└─────────────────────────────────────────────────────────────┘
```

### 主要功能模块

#### 1. 交接管理模块
- **交接查询**：多条件搜索、数据导出、状态筛选
- **交接登录**：问题记录、文件上传、责任人分配
- **交接分析**：数据统计、趋势分析、图表展示

#### 2. 故障管理模块
- **故障分析**：故障统计、原因分析、趋势预测
- **故障列表**：故障记录、状态跟踪、处理流程

#### 3. 现场管理模块
- **现场管理**：设备状态监控、现场数据采集
- **TactTime**：生产节拍分析、效率统计
- **领班业绩**：KPI数据统计、绩效分析

#### 4. 备品管理模块
- **备品列表**：库存查询、规格管理、供应商信息
- **备品请购**：需求申请、审批流程、采购跟踪
- **备品录入**：信息录入、批量导入（管理员权限）
- **备品履历**：使用记录、维护历史、成本分析

#### 5. 部件BOM管理
- **BOM查询**：部件清单、层级结构、版本管理
- **BOM登录**：结构录入、变更管理、审核流程

#### 6. 切机管理模块（已更新）
- **切机查询**：金型信息查询、需求分析、库存对比
- **切机履历**：操作历史、人员记录、时间跟踪

#### 7. PM管理模块
- **PM查询**：维护记录、计划查询、执行状态
- **PM登录**：维护计划、执行记录、结果反馈

#### 8. 电池管理模块
- **电池更换履历**：AGV电池更换记录、使用寿命
- **电池更换登录**：更换操作、人员记录
- **AGV电池信息**：电池状态、性能参数、预警管理

#### 9. 资料管理模块
- **资料手册**：技术文档、操作手册、规范标准
- **资料上传**：文档管理、版本控制、权限分配

#### 10. 通知发布模块
- **发布通知**：系统公告、紧急通知、定时发布
- **历史记录**：通知归档、阅读统计、反馈追踪

#### 11. 参数监控模块
- **实时监控**：设备参数实时显示、异常报警
- **基准管理**：参数标准、阈值设置、规格管理

#### 12. AI小助理
- **智能对话**：问题解答、操作指导、知识查询

### 项目架构类型
**前后端分离的单体Web应用**，采用传统的LAMP架构模式，通过Ajax进行前后端数据交互，支持多数据源连接。

## 2. 前端技术栈

### 核心框架/库
- **HTML5 + CSS3 + JavaScript (ES6+)**：现代Web标准
- **jQuery 1.11.3**：DOM操作和Ajax请求
- **Layui 2.2.3**：企业级UI组件库，提供导航、表格、表单等组件

### UI组件库和样式框架
- **Layui UI框架**：
  - 导航组件 (layui-nav)
  - 标签页组件 (layui-tab)
  - 表格组件 (layui-table)
  - 表单组件 (layui-form)
  - 弹层组件 (layui-layer)
- **自定义CSS样式**：
  - 响应式布局设计
  - 扁平化UI风格
  - 企业级配色方案

### 数据可视化
- **ECharts 5.x**：用于参数监控模块的图表展示
  - 实时数据曲线图
  - 多维度数据分析
  - 交互式图表操作

### 状态管理
- **localStorage**：用户信息和权限管理
- **sessionStorage**：临时数据存储
- **JavaScript对象**：页面级状态管理

### 构建工具和打包器
- **无构建工具**：采用传统的文件引用方式
- **直接引用CDN/本地文件**：所有依赖库通过script标签引入

### 开发工具和依赖包
```javascript
// 主要JavaScript库
- jquery-1.11.3.min.js     // DOM操作和Ajax
- layui.js                 // UI组件库
- echarts.js              // 图表库
- xlsx.js                 // Excel文件处理
- html2canvas.js          // 页面截图功能
- datatables.js           // 数据表格增强
```

### 前端特性
- **响应式设计**：支持桌面端和移动端
- **模块化架构**：每个功能模块独立的JS文件
- **组件化开发**：可复用的UI组件
- **权限控制**：基于用户级别的功能权限管理

## 3. 后端技术栈

### 编程语言和版本
- **PHP 7.4+**：服务端脚本语言
- **支持PHP 8.0+**：向上兼容

### Web框架
- **原生PHP**：无框架，直接使用PHP原生功能
- **面向过程编程**：简单直接的开发模式
- **模块化设计**：按功能模块组织PHP文件

### 数据库系统

#### 主数据库 (MySQL)
```php
// MySQL配置 (db_config.php)
$servername = "localhost";
$username = "root"; 
$password = "";
$dbname = "equipment_management";
```
- **MySQL 5.7+ / MariaDB 10.4+**
- **字符集**：utf8mb4 (支持完整Unicode)
- **存储引擎**：InnoDB (支持事务)

#### 辅助数据库 (SQL Server)
```php
// SQL Server配置 (sqlsrv_config.php)
$serverName = "************";
$database = "Tact_Time";
$username = "eqplink";
```
- **Microsoft SQL Server**：用于TactTime等特定模块
- **双数据库架构**：MySQL主库 + SQL Server辅库

### 服务器技术
- **Apache 2.4+ / Nginx 1.18+**：Web服务器
- **XAMPP开发环境**：本地开发栈
- **Linux/Windows服务器**：生产环境支持

### API设计模式
- **RESTful API风格**：标准HTTP方法
- **JSON数据格式**：统一的数据交换格式
- **统一响应格式**：
```php
// 标准API响应格式
{
    "success": true/false,
    "data": {...},
    "message": "操作结果信息"
}
```

### 认证和授权机制
- **Session-based认证**：PHP Session管理
- **多级权限控制**：
  - level: 用户级别 (21=OC, 22=LCM, 23=LOG)
  - splevel: 备品管理权限级别
- **基于角色的访问控制 (RBAC)**：
```php
// 权限验证示例
if ($userInfo->splevel === 1) {
    // 备品管理员权限
}
```

## 4. 开发环境和工具

### 包管理器
- **无包管理器**：手动管理依赖
- **直接文件引用**：所有库文件本地化存储

### 版本控制
- **Git**：源代码版本控制
- **文件结构化管理**：按功能模块组织代码

### 测试框架
- **手动测试**：无自动化测试框架
- **浏览器调试**：Chrome DevTools
- **PHP错误日志**：php_errors.log

### 代码质量工具
- **PHP内置错误报告**：error_reporting(E_ALL)
- **浏览器控制台**：JavaScript调试
- **手动代码审查**：团队协作审查

### 开发工具链
```
开发环境：
├── XAMPP (Apache + MySQL + PHP)
├── VS Code / PhpStorm (IDE)
├── Chrome DevTools (调试)
├── phpMyAdmin (数据库管理)
├── Git (版本控制)
└── SQL Server Management Studio (SQL Server管理)
```

### 项目目录结构
```
equipment_management/
├── associate/                     # 交接管理模块
│   ├── associate2.js              # 前端逻辑
│   ├── get_associates.php         # 获取交接记录
│   ├── submit_associate.php       # 提交交接记录
│   ├── modify_associate.php       # 修改交接记录
│   └── delete_associate_and_file.php # 删除记录和文件
├── bomlist/                       # BOM管理模块
│   ├── partsBom.js               # 前端逻辑
│   ├── get_parts_bom.php         # 获取BOM数据
│   ├── submit_parts_bom.php      # 提交BOM数据
│   └── sqlsrv_config.php         # SQL Server配置
├── css/                          # 样式文件
│   ├── style.css                 # 主样式文件
│   ├── index.css                 # 首页样式
│   └── layui.css                 # UI框架样式
├── js/                           # JavaScript文件
│   ├── components.js             # 公共组件
│   ├── auth.js                   # 权限控制
│   ├── tabs.js                   # 选项卡功能
│   ├── layui.js                  # UI框架
│   ├── jquery-1.11.3.min.js     # jQuery库
│   ├── echarts.js                # 图表库
│   ├── xlsx.js                   # Excel处理
│   └── *.js                      # 各模块前端脚本
├── ModelChange/                  # 切机管理模块
│   ├── ModelChange.html          # 页面文件
│   ├── ModelChange.js            # 前端逻辑
│   └── get_jig.php              # 获取金型数据
├── paramdata/                    # 参数监控模块
│   ├── index.html               # 监控页面
│   ├── js/main.js               # 前端逻辑
│   └── php/                     # 后端API
│       ├── config.php           # 配置文件
│       ├── get_data.php         # 获取监控数据
│       ├── get_specs.php        # 获取规格数据
│       └── update_spec.php      # 更新规格
├── php/                         # 主要后端API
│   ├── db_config.php            # MySQL数据库配置
│   ├── sqlsrv_config.php        # SQL Server配置
│   ├── login.php                # 用户登录
│   ├── session-manager.php      # 会话管理
│   ├── get_*.php               # 各种查询接口
│   ├── submit_*.php            # 各种提交接口
│   ├── update_*.php            # 各种更新接口
│   └── delete_*.php            # 各种删除接口
├── pic/                         # 图片资源
├── uploads/                     # 文件上传目录
├── logs/                        # 日志文件
├── error/                       # 错误页面
├── font/                        # 字体文件
├── index.html                   # 系统主页
├── login.html                   # 登录页面
├── *.html                       # 各功能模块页面
└── 技术文档.md                   # 技术文档
```

## 5. 部署和运维

### 部署方式
- **传统Web部署**：文件直接上传到Web服务器
- **LAMP/WAMP栈**：Linux/Windows + Apache + MySQL + PHP
- **手动部署**：FTP/SFTP文件传输

### 服务器环境
```
生产环境要求：
├── PHP 7.4+ (推荐 8.0+)
├── MySQL 5.7+ 或 MariaDB 10.3+
├── Apache 2.4+ 或 Nginx 1.18+
├── SSL证书 (HTTPS支持)
└── 文件上传权限配置
```

### 容器化技术
- **暂无容器化**：传统部署方式
- **可扩展支持**：可迁移至Docker容器

### 运维特性
- **日志管理**：
  - PHP错误日志：`php/php_errors.log`
  - 聊天记录：`logs/chat_logs.txt`
  - 应用日志：`paramdata/logs/app.log`
- **错误处理**：自定义错误页面 (error目录)
- **性能监控**：基础的PHP性能日志
- **数据备份**：数据库定期备份策略

### 安全特性
- **SQL注入防护**：使用预处理语句
- **XSS防护**：输入数据过滤
- **CORS配置**：跨域请求控制
- **文件上传安全**：文件类型和大小限制
- **Session安全**：安全的会话管理

## 6. 数据库设计

### 数据库连接配置

#### MySQL主数据库配置
```php
// php/db_config.php
$servername = "localhost";        // 本地开发: localhost
$username = "root";               // 生产环境需更改
$password = "";                   // 生产环境需设置密码
$dbname = "equipment_management"; // 主数据库名
$charset = "utf8mb4";            // 支持完整Unicode
```

#### SQL Server辅助数据库配置
```php
// php/sqlsrv_config.php 和 bomlist/sqlsrv_config.php
$serverName = "************";
$connectionOptions = array(
    "Database" => "Tact_Time",    // TactTime数据
    "Uid" => "eqplink",
    "PWD" => "eqplink",
    "CharacterSet" => "UTF-8"
);
```

### 主要数据表结构

#### 用户管理模块
```sql
-- 用户登录表
CREATE TABLE loginlist (
    account VARCHAR(50) PRIMARY KEY,     -- 账号
    password VARCHAR(255) NOT NULL,      -- 密码(加密)
    name VARCHAR(100) NOT NULL,          -- 姓名
    department VARCHAR(100),             -- 部门
    section VARCHAR(100),                -- 科室
    level INT DEFAULT 23,                -- 用户级别(21=OC, 22=LCM, 23=LOG)
    splevel INT DEFAULT 0,               -- 备品管理权限(1=管理员)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 交接管理模块
```sql
-- 交接记录表
CREATE TABLE associates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    line VARCHAR(50),                    -- 产线
    unit VARCHAR(50),                    -- 单元
    phenomenon TEXT,                     -- 现象描述
    cause TEXT,                          -- 原因分析
    treatment TEXT,                      -- 处理内容
    fault_class VARCHAR(100),            -- 故障分类
    fault_part VARCHAR(100),             -- 故障部件
    fault_code VARCHAR(50),              -- 故障代码
    status ENUM('open', 'closed') DEFAULT 'open', -- 状态
    reporter VARCHAR(100),               -- 报告人
    handler VARCHAR(100),                -- 处理人
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_line_unit (line, unit),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 交接文件表
CREATE TABLE associate_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    associate_id INT,
    filename VARCHAR(255),
    original_name VARCHAR(255),
    file_path VARCHAR(500),
    file_size INT,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (associate_id) REFERENCES associates(id) ON DELETE CASCADE
);
```

#### 备品管理模块
```sql
-- 备品主表
CREATE TABLE spareparts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    part_name VARCHAR(200) NOT NULL,     -- 部件名称
    part_number VARCHAR(100) UNIQUE,     -- 部件编号
    specification TEXT,                  -- 规格说明
    quantity INT DEFAULT 0,              -- 库存数量
    unit VARCHAR(20),                    -- 单位
    location VARCHAR(100),               -- 存放位置
    supplier VARCHAR(200),               -- 供应商
    price DECIMAL(10,2),                 -- 单价
    min_stock INT DEFAULT 0,             -- 最低库存
    category VARCHAR(50),                -- 分类
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_part_number (part_number),
    INDEX idx_category (category),
    INDEX idx_quantity (quantity)
);

-- 备品履历表
CREATE TABLE sparepart_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sparepart_id INT,
    operation_type ENUM('in', 'out', 'adjust'), -- 操作类型
    quantity_change INT,                 -- 数量变化
    quantity_after INT,                  -- 操作后数量
    reason TEXT,                         -- 操作原因
    operator VARCHAR(100),               -- 操作人
    operation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sparepart_id) REFERENCES spareparts(id)
);
```

#### 故障管理模块
```sql
-- 故障记录表
CREATE TABLE faults (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fault_id VARCHAR(50) UNIQUE,         -- 故障编号
    line VARCHAR(50),                    -- 产线
    unit VARCHAR(50),                    -- 单元
    fault_description TEXT,              -- 故障描述
    fault_cause TEXT,                    -- 故障原因
    solution TEXT,                       -- 解决方案
    fault_type VARCHAR(100),             -- 故障类型
    severity ENUM('low', 'medium', 'high', 'critical'), -- 严重程度
    status ENUM('open', 'in_progress', 'resolved', 'closed'),
    reporter VARCHAR(100),               -- 报告人
    assignee VARCHAR(100),               -- 负责人
    reported_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    INDEX idx_status (status),
    INDEX idx_line_unit (line, unit),
    INDEX idx_severity (severity)
);
```

#### 通知管理模块
```sql
-- 通知表
CREATE TABLE notices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,         -- 通知标题
    content TEXT,                        -- 通知内容
    category VARCHAR(50),                -- 通知分类
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    publisher VARCHAR(100),              -- 发布人
    status ENUM('draft', 'published', 'expired') DEFAULT 'draft',
    publish_time TIMESTAMP NULL,         -- 发布时间
    end_time TIMESTAMP NULL,             -- 结束时间
    target_audience TEXT,                -- 目标受众
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_publish_time (publish_time)
);

-- 通知文件表
CREATE TABLE notice_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    notice_id INT,
    filename VARCHAR(255),
    file_path VARCHAR(500),
    file_size INT,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (notice_id) REFERENCES notices(id) ON DELETE CASCADE
);
```

#### 参数监控模块
```sql
-- 设备参数数据表
CREATE TABLE eqp_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    device_mark VARCHAR(100),            -- 设备标识
    model_mark VARCHAR(100),             -- 型号标识
    param_name VARCHAR(100),             -- 参数名称
    param_value DECIMAL(15,6),           -- 参数值
    param_spec VARCHAR(100),             -- 参数规格
    param_unit VARCHAR(20),              -- 参数单位
    trigger_time_new TIMESTAMP,          -- 触发时间
    data_source VARCHAR(50),             -- 数据源
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_device_param (device_mark, param_name),
    INDEX idx_trigger_time (trigger_time_new)
);

-- 参数规格表
CREATE TABLE param_specs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    device_mark VARCHAR(100),
    param_name VARCHAR(100),
    min_value DECIMAL(15,6),             -- 最小值
    max_value DECIMAL(15,6),             -- 最大值
    target_value DECIMAL(15,6),          -- 目标值
    unit VARCHAR(20),                    -- 单位
    tolerance DECIMAL(15,6),             -- 容差
    updated_by VARCHAR(100),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_device_param (device_mark, param_name)
);
```

#### 教学资料模块
```sql
-- 教学资料表
CREATE TABLE teaching_materials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,         -- 资料标题
    description TEXT,                    -- 资料描述
    category VARCHAR(50),                -- 分类
    file_path VARCHAR(500),              -- 文件路径
    file_name VARCHAR(255),              -- 文件名
    file_size INT,                       -- 文件大小
    file_type VARCHAR(50),               -- 文件类型
    uploader VARCHAR(100),               -- 上传者
    download_count INT DEFAULT 0,        -- 下载次数
    status ENUM('active', 'inactive') DEFAULT 'active',
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_uploader (uploader)
);
```

#### BOM管理模块
```sql
-- BOM主表 (存储在SQL Server)
-- 通过bomlist/*.php文件访问SQL Server数据库
-- 主要字段：BOM编号、部件名称、规格、数量等
```

#### 电池管理模块
```sql
-- AGV电池信息表
CREATE TABLE agv_batteries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    battery_id VARCHAR(50) UNIQUE,       -- 电池编号
    agv_id VARCHAR(50),                  -- AGV编号
    battery_type VARCHAR(50),            -- 电池类型
    capacity INT,                        -- 电池容量(mAh)
    voltage DECIMAL(5,2),                -- 电压(V)
    manufacture_date DATE,               -- 生产日期
    install_date DATE,                   -- 安装日期
    status ENUM('active', 'maintenance', 'retired') DEFAULT 'active',
    last_maintenance DATE,               -- 最后维护日期
    cycle_count INT DEFAULT 0,           -- 充电循环次数
    health_percentage DECIMAL(5,2) DEFAULT 100.00, -- 健康度百分比
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_agv_id (agv_id),
    INDEX idx_status (status)
);

-- 电池更换记录表
CREATE TABLE battery_changes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    agv_id VARCHAR(50),                  -- AGV编号
    old_battery_id VARCHAR(50),          -- 旧电池编号
    new_battery_id VARCHAR(50),          -- 新电池编号
    change_reason TEXT,                  -- 更换原因
    operator VARCHAR(100),               -- 操作人员
    change_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,                          -- 备注
    INDEX idx_agv_id (agv_id),
    INDEX idx_change_date (change_date)
);
```

### 数据库特性和优化

#### 性能优化
- **索引策略**：关键查询字段建立复合索引
- **分区表**：大数据量表按时间分区
- **查询优化**：使用EXPLAIN分析查询性能
- **连接池**：数据库连接复用

#### 数据安全
- **字符集**：utf8mb4 支持完整Unicode和Emoji
- **数据备份**：定期自动备份策略
- **访问控制**：数据库用户权限最小化
- **SQL注入防护**：预处理语句和参数绑定

#### 扩展性设计
- **水平扩展**：支持读写分离和分库分表
- **多数据源**：MySQL主库 + SQL Server辅库
- **缓存层**：Redis缓存热点数据（可扩展）
- **数据同步**：跨数据库数据同步机制

## 7. API接口文档

### 接口设计规范

#### 统一响应格式
```json
{
    "success": true,              // 操作是否成功
    "data": {},                   // 返回数据
    "message": "操作成功",         // 返回消息
    "code": 200                   // 状态码(可选)
}
```

#### 错误响应格式
```json
{
    "success": false,
    "data": null,
    "message": "错误描述",
    "error": "详细错误信息"
}
```

### 核心API接口

#### 用户认证接口
```php
POST /php/login.php
// 用户登录
参数：
- account: string (必需) - 用户账号
- password: string (必需) - 用户密码

返回：
{
    "success": true,
    "data": {
        "account": "user001",
        "name": "张三",
        "department": "设备部",
        "section": "维修组",
        "level": 21,
        "splevel": 1,
        "loginDate": "2024-01-01"
    }
}
```

#### 交接管理接口
```php
// 获取交接记录
GET /associate/get_associates.php?page=1&limit=20&status=open&line=A1

// 提交交接记录
POST /associate/submit_associate.php
参数：line, unit, phenomenon, cause, treatment, fault_class, fault_part, fault_code

// 修改交接记录
POST /associate/modify_associate.php
参数：id, [其他字段]

// 删除交接记录
POST /associate/delete_associate_and_file.php
参数：id

// 获取交接分析数据
GET /associate/get_associates_analysis.php?date_from=2024-01-01&date_to=2024-01-31

// 获取交接文件
GET /associate/get_associate_files.php?id=123
```

#### 备品管理接口
```php
// 获取备品列表
GET /php/get_spareparts.php?page=1&limit=20&category=电机&keyword=搜索关键词

// 提交备品信息
POST /php/submit_spareparts.php
参数：part_name, part_number, specification, quantity, unit, location, supplier, price

// 更新备品信息
POST /php/update_sparepart.php
参数：id, [更新字段]

// 删除备品
POST /php/delete_sparepart.php
参数：id

// 获取备品详情
GET /php/get_sparepart_detail.php?id=123

// 获取备品履历
GET /php/get_sparepart_history.php?sparepart_id=123&page=1&limit=20

// 添加备品履历
POST /php/add_sparepart_history.php
参数：sparepart_id, operation_type, quantity_change, reason
```

#### 故障管理接口
```php
// 获取故障列表
GET /php/get_faults.php?page=1&limit=20&status=open&severity=high

// 提交故障记录
POST /php/submit_fault.php
参数：line, unit, fault_description, fault_cause, solution, fault_type, severity

// 更新故障状态
POST /php/update_fault_status.php
参数：id, status, assignee

// 删除故障记录
POST /php/delete_fault.php
参数：id

// 获取故障文件
GET /php/get_fault_files.php?fault_id=123
```

#### 通知管理接口
```php
// 获取通知列表
GET /php/get_notices.php?page=1&limit=20&status=published

// 获取首页活跃通知
GET /php/get_active_notices.php

// 发布通知
POST /php/submit_notice.php
参数：title, content, category, priority, end_time, target_audience

// 删除通知
POST /php/delete_notice.php
参数：id

// 获取通知文件
GET /php/get_notice_files.php?notice_id=123
```

#### 参数监控接口
```php
// 获取监控数据
GET /paramdata/php/get_data.php?device=DEV001&start_time=2024-01-01&end_time=2024-01-02

// 获取设备选项
GET /paramdata/php/get_options.php

// 获取参数规格
GET /paramdata/php/get_specs.php?device=DEV001

// 更新参数规格
POST /paramdata/php/update_spec.php
参数：device_mark, param_name, min_value, max_value, target_value

// 导出监控数据
GET /paramdata/php/export_data.php?device=DEV001&format=excel
```

#### 切机管理接口
```php
// 获取金型数据
GET /ModelChange/get_jig.php
返回：{
    "demand": [...],     // 需求数据
    "inventory": {...}   // 库存数据
}
```

#### BOM管理接口
```php
// 获取BOM数据 (SQL Server)
GET /bomlist/get_parts_bom.php?page=1&limit=20&search=关键词

// 提交BOM数据
POST /bomlist/submit_parts_bom.php
参数：part_number, part_name, specification, quantity

// 修改BOM数据
POST /bomlist/modify_parts_bom.php
参数：id, [更新字段]

// 获取BOM选项
GET /bomlist/get_options.php
```

#### PM管理接口
```php
// 获取PM记录
GET /php/get_associates_pm.php?page=1&limit=20&date_from=2024-01-01

// 提交PM记录
POST /php/submit_associate_pm.php
参数：line, unit, pm_type, pm_content, pm_result
```

#### 电池管理接口
```php
// 获取电池信息
GET /php/get_battery.php?page=1&limit=20&agv_id=AGV001

// 获取电池更换记录
GET /php/get_battery_change.php?agv_id=AGV001&page=1&limit=20

// 提交电池信息
POST /php/submit_battery.php
参数：battery_id, agv_id, battery_type, capacity, voltage

// 更新电池信息
POST /php/update_battery.php
参数：id, [更新字段]
```

#### 教学资料接口
```php
// 获取资料列表
GET /php/get_teaching.php?page=1&limit=20&category=操作手册

// 提交资料
POST /php/submit_teaching.php
参数：title, description, category, file (文件上传)

// 获取资料文件
GET /php/get_teaching_files.php?id=123
```

#### TactTime接口
```php
// 获取TactTime数据
GET /php/get_tt.php?line=A1&date=2024-01-01

// 获取TactTime表格数据
GET /php/get_tt_table.php?line=A1&start_date=2024-01-01&end_date=2024-01-31

// 获取TactTime选项
GET /php/get_tt_options.php
```

#### KPI管理接口
```php
// 获取KPI数据
GET /php/get_kpi.php?department=设备部&date=2024-01-01

// 提交KPI数据
POST /php/submit_kpi.php
参数：department, kpi_type, kpi_value, target_value, date

// 更新KPI数据
POST /php/update_kpi.php
参数：id, [更新字段]
```

#### AI聊天接口
```php
// 发送聊天消息
POST /php/chat.php
参数：message, user_id

// 记录聊天日志
POST /php/log_chat.php
参数：user_id, message, response, timestamp
```

### 文件上传接口

#### 通用文件上传
```php
POST /php/upload_file.php
参数：
- file: 文件对象
- module: 模块名称 (associate/notice/teaching等)
- related_id: 关联记录ID

返回：
{
    "success": true,
    "data": {
        "filename": "generated_filename.ext",
        "original_name": "original_filename.ext",
        "file_path": "uploads/module/filename.ext",
        "file_size": 1024000
    }
}
```

#### 文件下载接口
```php
GET /php/download_file.php?module=teaching&file=filename.ext
GET /php/download_battery_file.php?file=filename.ext
```

### 权限控制

#### 权限级别说明
- **level 21 (OC)**: 最高权限，可访问所有功能
- **level 22 (LCM)**: 中级权限，可管理大部分功能  
- **level 23 (LOG)**: 基础权限，只能查看和基本操作
- **splevel 1**: 备品管理员权限，可录入和管理备品

#### 权限验证示例
```php
// 权限检查函数
function checkPermission($required_level, $user_level) {
    return $user_level <= $required_level;
}

// 备品管理权限检查
function checkSparepartPermission($user_splevel) {
    return $user_splevel === 1;
}
```

## 8. 部署配置文档

### 系统要求

#### 服务器环境
```
操作系统：Linux (推荐 Ubuntu 20.04+) 或 Windows Server 2019+
Web服务器：Apache 2.4+ 或 Nginx 1.18+
PHP版本：PHP 7.4+ (推荐 PHP 8.0+)
数据库：MySQL 8.0+ 或 MariaDB 10.4+
内存：最低 4GB，推荐 8GB+
存储：最低 50GB，推荐 100GB+
```

#### PHP扩展要求
```
必需扩展：
- mysqli (MySQL连接)
- sqlsrv (SQL Server连接，如需连接SQL Server)
- json (JSON处理)
- fileinfo (文件信息)
- mbstring (多字节字符串)
- gd (图像处理)

推荐扩展：
- opcache (性能优化)
- redis (缓存，可选)
- zip (压缩文件处理)
```

### 部署步骤

#### 1. 环境准备
```bash
# Ubuntu系统安装LAMP环境
sudo apt update
sudo apt install apache2 mysql-server php php-mysql php-json php-mbstring php-gd

# 启动服务
sudo systemctl start apache2
sudo systemctl start mysql
sudo systemctl enable apache2
sudo systemctl enable mysql
```

#### 2. 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE equipment_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'eqp_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON equipment_management.* TO 'eqp_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 3. 代码部署
```bash
# 克隆代码到Web目录
cd /var/www/html
git clone [repository_url] equipment_management
cd equipment_management

# 设置目录权限
sudo chown -R www-data:www-data .
sudo chmod -R 755 .
sudo chmod -R 777 uploads/
sudo chmod -R 777 logs/
```

#### 4. 配置文件修改
```php
// 修改 php/db_config.php
$servername = "localhost";
$username = "eqp_user";
$password = "secure_password";
$dbname = "equipment_management";

// 修改 php/sqlsrv_config.php (如需SQL Server连接)
$serverName = "sql_server_ip";
$connectionOptions = array(
    "Database" => "production_db",
    "Uid" => "sql_user",
    "PWD" => "sql_password"
);
```

#### 5. Apache虚拟主机配置
```apache
<VirtualHost *:80>
    ServerName equipment.company.com
    DocumentRoot /var/www/html/equipment_management
    
    <Directory /var/www/html/equipment_management>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/equipment_error.log
    CustomLog ${APACHE_LOG_DIR}/equipment_access.log combined
</VirtualHost>
```

#### 6. SSL配置 (推荐)
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-apache

# 获取SSL证书
sudo certbot --apache -d equipment.company.com
```

### 性能优化配置

#### PHP配置优化 (php.ini)
```ini
# 内存限制
memory_limit = 256M

# 文件上传
upload_max_filesize = 50M
post_max_size = 50M
max_file_uploads = 20

# 执行时间
max_execution_time = 300

# OPcache优化
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=10000
opcache.validate_timestamps=0
```

#### MySQL配置优化 (my.cnf)
```ini
[mysqld]
# InnoDB优化
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_method = O_DIRECT

# 查询缓存
query_cache_type = 1
query_cache_size = 64M

# 连接数
max_connections = 200
```

### 监控和维护

#### 日志监控
```bash
# 设置日志轮转
sudo nano /etc/logrotate.d/equipment_management

/var/www/html/equipment_management/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    create 644 www-data www-data
}
```

#### 数据库备份
```bash
#!/bin/bash
# 数据库自动备份脚本
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

mysqldump -u eqp_user -p equipment_management > $BACKUP_DIR/equipment_$DATE.sql
find $BACKUP_DIR -name "equipment_*.sql" -mtime +30 -delete
```

#### 系统监控
```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 监控脚本示例
#!/bin/bash
# 检查系统资源
df -h
free -h
ps aux | head -20
```

---

**文档版本**：v2.0  
**最后更新**：2024-12-16  
**维护团队**：设备管理系统开发组

### 更新历史
- 2024-12-16: v2.0 - 全面重构技术文档，新增完整的API接口文档、数据库结构文档和部署配置文档
- 2025-09-06: v1.1 - fault.js 添加删除功能和权限优化，fault.css 添加删除按钮样式
- 2025-08-18: v1.0 - 初始版本
