document.addEventListener("DOMContentLoaded", function () {
  // 默认加载切机查询数据
  loadDemandTable();
});

// 选项卡切换函数
function switchTab(tabName) {
  // 隐藏所有选项卡内容
  document.querySelectorAll('.tab-content').forEach(tab => {
    tab.classList.remove('active');
  });
  
  // 移除所有按钮的激活状态
  document.querySelectorAll('.tab-button').forEach(btn => {
    btn.classList.remove('active');
  });
  
  // 显示选中的选项卡
  const targetTab = document.getElementById(tabName + 'Tab');
  if (targetTab) {
    targetTab.classList.add('active');
  }
  
  // 激活对应的按钮
  const targetButton = document.querySelector(`[onclick="switchTab('${tabName}')"]`);
  if (targetButton) {
    targetButton.classList.add('active');
  }
  
  // 根据选项卡加载对应数据
  if (tabName === 'search') {
    loadDemandTable();
  } else if (tabName === 'history') {
    loadHistoryData();
  }
}

async function loadDemandTable() {
  const table = document.querySelector(".data-table");
  try {
    showLoading(table);
    const response = await fetch("get_jig.php");
    const { demand, inventory } = await response.json();
    const { dates, cuts, matrix } = transformDemandData(demand, inventory);
    renderDemandTable(dates, cuts, matrix);
  } catch (error) {
    console.error("数据加载失败:", error);
    showError(table, error);
  }
}

function transformDemandData(demandData, inventoryData) {
  const dates = [...new Set(demandData.map((d) => d.rdate))].sort(
    (a, b) => new Date(a) - new Date(b)
  );
  const cuts = [...new Set(demandData.map((d) => d.CUT))].sort((a, b) =>
    a.localeCompare(b)
  );

  const matrix = cuts.map((cut) => {
    const row = { cut, inventory: inventoryData[cut] || 0 };
    const demands = [];
    dates.forEach((date) => {
      const demand = demandData.find((d) => d.CUT === cut && d.rdate === date);
      const value = demand ? parseInt(demand.TotalDemand, 10) : 0;
      row[date] = value;
      demands.push(value);
    });
    const sortedDemands = [...demands].sort((a, b) => b - a);
    row.maxDemand = sortedDemands[0] || 0;
    return row;
  });

  return { dates, cuts, matrix };
}

function renderDemandTable(dates, cuts, matrix) {
  const table = document.querySelector(".data-table");
  table.innerHTML = "";

  const thead = document.createElement("thead");
  thead.innerHTML = `
        <tr>
            <th>金型</th>
            <th>当前库存</th>
            <th>最大需求</th>
            <th>差异</th>
            ${dates.map((date) => `<th>${date}</th>`).join("")}
        </tr>
    `;
  table.appendChild(thead);

  const tbody = document.createElement("tbody");
  matrix.forEach((rowData) => {
    const tr = document.createElement("tr");
    tr.innerHTML = `
            <td>${rowData.cut}</td>
            <td class="inventory-cell">${rowData.inventory}</td>
            <td class="inventory-cell">${rowData.maxDemand}</td>
            <td class=" ${
              rowData.inventory - rowData.maxDemand < 0
                ? "highlight"
                : "inventory-cell"
            }">${rowData.inventory - rowData.maxDemand}</td>
            ${dates
              .map(
                (date) => `
                <td class="${
                  rowData[date] > rowData.inventory ? "highlight" : ""
                }">
                    ${rowData[date]}
                </td>
            `
              )
              .join("")}
        `;
    tbody.appendChild(tr);
  });
  table.appendChild(tbody);
}

function showLoading(table) {
  table.innerHTML = `
        <tr>
            <td colspan="100%" style="text-align: center; padding: 40px;">
                <div class="loading-spinner"></div>
                <div>数据加载中...</div>
            </td>
        </tr>
    `;
}

function showError(table, error) {
  table.innerHTML = `
        <tr>
            <td colspan="100%" style="color: red; padding: 20px;">
                数据加载失败：${error.message}
            </td>
        </tr>
    `;
}

// 加载切机履历数据
async function loadHistoryData() {
  const tbody = document.getElementById('historyTableBody');
  if (!tbody) return;
  
  try {
    // 显示加载状态
    tbody.innerHTML = `
      <tr>
        <td colspan="6" style="text-align: center; padding: 40px;">
          <div class="loading-spinner"></div>
          <div>数据加载中...</div>
        </td>
      </tr>
    `;
    
    // 这里可以添加实际的API调用
    // const response = await fetch('get_history.php');
    // const historyData = await response.json();
    
    // 临时模拟数据
    setTimeout(() => {
      tbody.innerHTML = `
        <tr>
          <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
            暂无履历数据
          </td>
        </tr>
      `;
    }, 1000);
    
  } catch (error) {
    console.error('履历数据加载失败:', error);
    tbody.innerHTML = `
      <tr>
        <td colspan="6" style="color: red; padding: 20px;">
          数据加载失败：${error.message}
        </td>
      </tr>
    `;
  }
}
